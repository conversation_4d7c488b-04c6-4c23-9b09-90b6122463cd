using HcAgents.Domain.Abstractions;
using HcAgents.Domain.Models.Response;
using MediatR;

namespace HcAgents.Application.Handlers.Commands;

public class CreateSessionCommandHandler
    : IRequestHandler<CreateSessionCommand, CreateSessionResponse>
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly IOtpService _otpService;

    public CreateSessionCommandHandler(IUnitOfWork unitOfWork, IOtpService otpService)
    {
        _unitOfWork = unitOfWork;
        _otpService = otpService;
    }

    public async Task<CreateSessionResponse> Handle(
        CreateSessionCommand request,
        CancellationToken cancellationToken
    )
    {
        try
        {
            var userExists = await _unitOfWork.UserRepository.GetUserByEmail(request.Email);

            if (userExists == null)
            {
                throw new Exception("UserNotExists");
            }

            if (!_otpService.ValidateOtp(request.Email, request.Otp))
            {
                throw new Exception("InvalidOtp");
            }

            _otpService.RemoveOtp(request.Email);

            return new CreateSessionResponse
            {
                UserId = userExists.Id,
                User = userExists,
                AccessToken = accessToken,
            };
        }
        catch (Exception)
        {
            throw;
        }
    }
}
