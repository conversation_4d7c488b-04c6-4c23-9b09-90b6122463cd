c:\Users\<USER>\OneDrive\Desktop\Projeto HC\hcagents-solution\Api\bin\Debug\net9.0\appsettings.Development.json
c:\Users\<USER>\OneDrive\Desktop\Projeto HC\hcagents-solution\Api\bin\Debug\net9.0\appsettings.json
c:\Users\<USER>\OneDrive\Desktop\Projeto HC\hcagents-solution\Api\bin\Debug\net9.0\Api.staticwebassets.endpoints.json
c:\Users\<USER>\OneDrive\Desktop\Projeto HC\hcagents-solution\Api\bin\Debug\net9.0\Api.exe
C:\Users\<USER>\OneDrive\Desktop\Projeto HC\hcagents-solution\Api\bin\Debug\net9.0\Api.deps.json
C:\Users\<USER>\OneDrive\Desktop\Projeto HC\hcagents-solution\Api\bin\Debug\net9.0\Api.runtimeconfig.json
c:\Users\<USER>\OneDrive\Desktop\Projeto HC\hcagents-solution\Api\bin\Debug\net9.0\Api.dll
c:\Users\<USER>\OneDrive\Desktop\Projeto HC\hcagents-solution\Api\bin\Debug\net9.0\Api.pdb
c:\Users\<USER>\OneDrive\Desktop\Projeto HC\hcagents-solution\Api\bin\Debug\net9.0\Microsoft.EntityFrameworkCore.dll
c:\Users\<USER>\OneDrive\Desktop\Projeto HC\hcagents-solution\Api\bin\Debug\net9.0\Microsoft.EntityFrameworkCore.Abstractions.dll
c:\Users\<USER>\OneDrive\Desktop\Projeto HC\hcagents-solution\Api\bin\Debug\net9.0\Microsoft.EntityFrameworkCore.Relational.dll
c:\Users\<USER>\OneDrive\Desktop\Projeto HC\hcagents-solution\Api\bin\Debug\net9.0\Microsoft.Extensions.DependencyInjection.dll
c:\Users\<USER>\OneDrive\Desktop\Projeto HC\hcagents-solution\Api\bin\Debug\net9.0\Microsoft.Extensions.DependencyInjection.Abstractions.dll
c:\Users\<USER>\OneDrive\Desktop\Projeto HC\hcagents-solution\Api\bin\Debug\net9.0\Application.dll
c:\Users\<USER>\OneDrive\Desktop\Projeto HC\hcagents-solution\Api\bin\Debug\net9.0\Common.dll
c:\Users\<USER>\OneDrive\Desktop\Projeto HC\hcagents-solution\Api\bin\Debug\net9.0\Domain.dll
c:\Users\<USER>\OneDrive\Desktop\Projeto HC\hcagents-solution\Api\bin\Debug\net9.0\Infrastructure.dll
c:\Users\<USER>\OneDrive\Desktop\Projeto HC\hcagents-solution\Api\bin\Debug\net9.0\Common.pdb
c:\Users\<USER>\OneDrive\Desktop\Projeto HC\hcagents-solution\Api\bin\Debug\net9.0\Application.pdb
c:\Users\<USER>\OneDrive\Desktop\Projeto HC\hcagents-solution\Api\bin\Debug\net9.0\Domain.pdb
c:\Users\<USER>\OneDrive\Desktop\Projeto HC\hcagents-solution\Api\bin\Debug\net9.0\Infrastructure.pdb
c:\Users\<USER>\OneDrive\Desktop\Projeto HC\hcagents-solution\Api\obj\Debug\net9.0\Api.csproj.AssemblyReference.cache
c:\Users\<USER>\OneDrive\Desktop\Projeto HC\hcagents-solution\Api\obj\Debug\net9.0\Api.GeneratedMSBuildEditorConfig.editorconfig
c:\Users\<USER>\OneDrive\Desktop\Projeto HC\hcagents-solution\Api\obj\Debug\net9.0\Api.AssemblyInfoInputs.cache
c:\Users\<USER>\OneDrive\Desktop\Projeto HC\hcagents-solution\Api\obj\Debug\net9.0\Api.AssemblyInfo.cs
c:\Users\<USER>\OneDrive\Desktop\Projeto HC\hcagents-solution\Api\obj\Debug\net9.0\Api.csproj.CoreCompileInputs.cache
c:\Users\<USER>\OneDrive\Desktop\Projeto HC\hcagents-solution\Api\obj\Debug\net9.0\Api.MvcApplicationPartsAssemblyInfo.cache
c:\Users\<USER>\OneDrive\Desktop\Projeto HC\hcagents-solution\Api\obj\Debug\net9.0\scopedcss\bundle\Api.styles.css
c:\Users\<USER>\OneDrive\Desktop\Projeto HC\hcagents-solution\Api\obj\Debug\net9.0\staticwebassets.build.json
c:\Users\<USER>\OneDrive\Desktop\Projeto HC\hcagents-solution\Api\obj\Debug\net9.0\staticwebassets.development.json
c:\Users\<USER>\OneDrive\Desktop\Projeto HC\hcagents-solution\Api\obj\Debug\net9.0\staticwebassets.build.endpoints.json
c:\Users\<USER>\OneDrive\Desktop\Projeto HC\hcagents-solution\Api\obj\Debug\net9.0\staticwebassets\msbuild.Api.Microsoft.AspNetCore.StaticWebAssets.props
c:\Users\<USER>\OneDrive\Desktop\Projeto HC\hcagents-solution\Api\obj\Debug\net9.0\staticwebassets\msbuild.Api.Microsoft.AspNetCore.StaticWebAssetEndpoints.props
c:\Users\<USER>\OneDrive\Desktop\Projeto HC\hcagents-solution\Api\obj\Debug\net9.0\staticwebassets\msbuild.build.Api.props
c:\Users\<USER>\OneDrive\Desktop\Projeto HC\hcagents-solution\Api\obj\Debug\net9.0\staticwebassets\msbuild.buildMultiTargeting.Api.props
c:\Users\<USER>\OneDrive\Desktop\Projeto HC\hcagents-solution\Api\obj\Debug\net9.0\staticwebassets\msbuild.buildTransitive.Api.props
c:\Users\<USER>\OneDrive\Desktop\Projeto HC\hcagents-solution\Api\obj\Debug\net9.0\staticwebassets.pack.json
C:\Users\<USER>\OneDrive\Desktop\Projeto HC\hcagents-solution\Api\obj\Debug\net9.0\Api.csproj.Up2Date
c:\Users\<USER>\OneDrive\Desktop\Projeto HC\hcagents-solution\Api\obj\Debug\net9.0\Api.dll
c:\Users\<USER>\OneDrive\Desktop\Projeto HC\hcagents-solution\Api\obj\Debug\net9.0\refint\Api.dll
c:\Users\<USER>\OneDrive\Desktop\Projeto HC\hcagents-solution\Api\obj\Debug\net9.0\Api.pdb
C:\Users\<USER>\OneDrive\Desktop\Projeto HC\hcagents-solution\Api\obj\Debug\net9.0\Api.genruntimeconfig.cache
C:\Users\<USER>\OneDrive\Desktop\Projeto HC\hcagents-solution\Api\obj\Debug\net9.0\ref\Api.dll
c:\Users\<USER>\OneDrive\Desktop\Projeto HC\hcagents-solution\Api\bin\Debug\net9.0\MediatR.dll
c:\Users\<USER>\OneDrive\Desktop\Projeto HC\hcagents-solution\Api\bin\Debug\net9.0\MediatR.Contracts.dll
c:\Users\<USER>\OneDrive\Desktop\Projeto HC\hcagents-solution\Api\bin\Debug\net9.0\Microsoft.IdentityModel.Abstractions.dll
c:\Users\<USER>\OneDrive\Desktop\Projeto HC\hcagents-solution\Api\bin\Debug\net9.0\Microsoft.IdentityModel.JsonWebTokens.dll
c:\Users\<USER>\OneDrive\Desktop\Projeto HC\hcagents-solution\Api\bin\Debug\net9.0\Microsoft.IdentityModel.Logging.dll
c:\Users\<USER>\OneDrive\Desktop\Projeto HC\hcagents-solution\Api\bin\Debug\net9.0\Microsoft.IdentityModel.Tokens.dll
c:\Users\<USER>\OneDrive\Desktop\Projeto HC\hcagents-solution\Api\bin\Debug\net9.0\MySqlConnector.dll
c:\Users\<USER>\OneDrive\Desktop\Projeto HC\hcagents-solution\Api\bin\Debug\net9.0\Pomelo.EntityFrameworkCore.MySql.dll
c:\Users\<USER>\OneDrive\Desktop\Projeto HC\hcagents-solution\Api\bin\Debug\net9.0\Microsoft.Extensions.Caching.Abstractions.dll
c:\Users\<USER>\OneDrive\Desktop\Projeto HC\hcagents-solution\Api\bin\Debug\net9.0\Microsoft.Extensions.Caching.Memory.dll
c:\Users\<USER>\OneDrive\Desktop\Projeto HC\hcagents-solution\Api\bin\Debug\net9.0\Microsoft.Extensions.Logging.Abstractions.dll
c:\Users\<USER>\OneDrive\Desktop\Projeto HC\hcagents-solution\Api\bin\Debug\net9.0\Microsoft.Extensions.Options.dll
c:\Users\<USER>\OneDrive\Desktop\Projeto HC\hcagents-solution\Api\bin\Debug\net9.0\Microsoft.Extensions.Primitives.dll
c:\Users\<USER>\OneDrive\Desktop\Projeto HC\hcagents-solution\Api\bin\Debug\net9.0\Microsoft.OpenApi.dll
c:\Users\<USER>\OneDrive\Desktop\Projeto HC\hcagents-solution\Api\bin\Debug\net9.0\Swashbuckle.AspNetCore.Swagger.dll
c:\Users\<USER>\OneDrive\Desktop\Projeto HC\hcagents-solution\Api\bin\Debug\net9.0\Swashbuckle.AspNetCore.SwaggerGen.dll
c:\Users\<USER>\OneDrive\Desktop\Projeto HC\hcagents-solution\Api\bin\Debug\net9.0\Swashbuckle.AspNetCore.SwaggerUI.dll
c:\Users\<USER>\OneDrive\Desktop\Projeto HC\hcagents-solution\Api\obj\Debug\net9.0\Api.MvcApplicationPartsAssemblyInfo.cs
c:\Users\<USER>\OneDrive\Desktop\Projeto HC\hcagents-solution\Api\bin\Debug\net9.0\Microsoft.AspNetCore.Authentication.JwtBearer.dll
c:\Users\<USER>\OneDrive\Desktop\Projeto HC\hcagents-solution\Api\bin\Debug\net9.0\Microsoft.IdentityModel.Protocols.dll
c:\Users\<USER>\OneDrive\Desktop\Projeto HC\hcagents-solution\Api\bin\Debug\net9.0\Microsoft.IdentityModel.Protocols.OpenIdConnect.dll
c:\Users\<USER>\OneDrive\Desktop\Projeto HC\hcagents-solution\Api\bin\Debug\net9.0\System.IdentityModel.Tokens.Jwt.dll
c:\Users\<USER>\OneDrive\Desktop\Projeto HC\hcagents-solution\Api\bin\Debug\net9.0\Microsoft.Extensions.Configuration.dll
c:\Users\<USER>\OneDrive\Desktop\Projeto HC\hcagents-solution\Api\bin\Debug\net9.0\Microsoft.Extensions.Configuration.Abstractions.dll
