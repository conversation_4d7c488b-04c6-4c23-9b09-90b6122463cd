using HcAgents.Domain.Commands;
using HcAgents.Domain.Entities;
using HcAgents.Domain.Models.Base;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace HcAgents.Api.Controllers;

[Route("[controller]")]
[ApiController]
public class SessionController : ControllerBase
{
    private readonly IMediator _mediator;

    public SessionController(IMediator mediator)
    {
        _mediator = mediator;
    }

    [HttpPost]
    [Route("create")]
    [AllowAnonymous]
    [ProducesResponseType(typeof(ApiResponse<bool>), StatusCodes.Status201Created)]
    [ProducesResponseType(typeof(ApiResponse<bool>), StatusCodes.Status400BadRequest)]
    [ProducesResponseType(typeof(ApiResponse<bool>), StatusCodes.Status500InternalServerError)]
    public async Task<ActionResult<ApiResponse<bool>>> CreateSession(
        [FromBody] CreateOtpCodeCommand command,
        CancellationToken cancellationToken
    )
    {
        try
        {
            var createdSession = await _mediator.Send(command, cancellationToken);

            return StatusCode(
                StatusCodes.Status201Created,
                new ApiResponse<bool>(
                    true,
                    StatusCodes.Status201Created,
                    "User created",
                    createdSession
                )
            );
        }
        catch (Exception ex)
        {
            switch (ex.Message)
            {
                case "UserNotExists":
                    return StatusCode(
                        StatusCodes.Status404NotFound,
                        new ApiResponse<User>(
                            false,
                            StatusCodes.Status404NotFound,
                            "User not exists",
                            ex.Message
                        )
                    );
                default:
                    return StatusCode(
                        StatusCodes.Status500InternalServerError,
                        new ApiResponse<User>(
                            false,
                            StatusCodes.Status500InternalServerError,
                            "Server Error creating user",
                            ex.Message
                        )
                    );
            }
        }
    }
}
