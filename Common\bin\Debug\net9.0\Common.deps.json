{"runtimeTarget": {"name": ".NETCoreApp,Version=v9.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v9.0": {"Common/1.0.0": {"dependencies": {"Application": "1.0.0", "Domain": "1.0.0", "Infrastructure": "1.0.0", "Microsoft.AspNetCore.Authentication.JwtBearer": "9.0.7", "Swashbuckle.AspNetCore.SwaggerGen": "9.0.3"}, "runtime": {"Common.dll": {}}}, "MediatR/13.0.0": {"dependencies": {"MediatR.Contracts": "2.0.1", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.7", "Microsoft.Extensions.Logging": "8.0.0", "Microsoft.IdentityModel.JsonWebTokens": "8.13.0"}, "runtime": {"lib/net9.0/MediatR.dll": {"assemblyVersion": "13.0.0.0", "fileVersion": "13.0.0.0"}}}, "MediatR.Contracts/2.0.1": {"runtime": {"lib/netstandard2.0/MediatR.Contracts.dll": {"assemblyVersion": "2.0.1.0", "fileVersion": "2.0.1.0"}}}, "Microsoft.AspNetCore.Authentication.JwtBearer/9.0.7": {"dependencies": {"Microsoft.IdentityModel.Protocols.OpenIdConnect": "8.0.1"}, "runtime": {"lib/net9.0/Microsoft.AspNetCore.Authentication.JwtBearer.dll": {"assemblyVersion": "9.0.7.0", "fileVersion": "9.0.725.31702"}}}, "Microsoft.EntityFrameworkCore/8.0.2": {"dependencies": {"Microsoft.EntityFrameworkCore.Abstractions": "8.0.2", "Microsoft.EntityFrameworkCore.Analyzers": "8.0.2", "Microsoft.Extensions.Caching.Memory": "9.0.7", "Microsoft.Extensions.Logging": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.dll": {"assemblyVersion": "8.0.2.0", "fileVersion": "8.0.224.6803"}}}, "Microsoft.EntityFrameworkCore.Abstractions/8.0.2": {"runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.Abstractions.dll": {"assemblyVersion": "8.0.2.0", "fileVersion": "8.0.224.6803"}}}, "Microsoft.EntityFrameworkCore.Analyzers/8.0.2": {}, "Microsoft.EntityFrameworkCore.Relational/8.0.2": {"dependencies": {"Microsoft.EntityFrameworkCore": "8.0.2", "Microsoft.Extensions.Configuration.Abstractions": "9.0.7"}, "runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.Relational.dll": {"assemblyVersion": "8.0.2.0", "fileVersion": "8.0.224.6803"}}}, "Microsoft.Extensions.Caching.Abstractions/9.0.7": {"dependencies": {"Microsoft.Extensions.Primitives": "9.0.7"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Caching.Abstractions.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.725.31616"}}}, "Microsoft.Extensions.Caching.Memory/9.0.7": {"dependencies": {"Microsoft.Extensions.Caching.Abstractions": "9.0.7", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.7", "Microsoft.Extensions.Logging.Abstractions": "9.0.7", "Microsoft.Extensions.Options": "9.0.7", "Microsoft.Extensions.Primitives": "9.0.7"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Caching.Memory.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.725.31616"}}}, "Microsoft.Extensions.Configuration/9.0.7": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.7", "Microsoft.Extensions.Primitives": "9.0.7"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Configuration.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.725.31616"}}}, "Microsoft.Extensions.Configuration.Abstractions/9.0.7": {"dependencies": {"Microsoft.Extensions.Primitives": "9.0.7"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Configuration.Abstractions.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.725.31616"}}}, "Microsoft.Extensions.DependencyInjection/9.0.7": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.7"}, "runtime": {"lib/net9.0/Microsoft.Extensions.DependencyInjection.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.725.31616"}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/9.0.7": {"runtime": {"lib/net9.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.725.31616"}}}, "Microsoft.Extensions.Logging/8.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection": "9.0.7", "Microsoft.Extensions.Logging.Abstractions": "9.0.7", "Microsoft.Extensions.Options": "9.0.7"}}, "Microsoft.Extensions.Logging.Abstractions/9.0.7": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.7"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Logging.Abstractions.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.725.31616"}}}, "Microsoft.Extensions.Options/9.0.7": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.7", "Microsoft.Extensions.Primitives": "9.0.7"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Options.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.725.31616"}}}, "Microsoft.Extensions.Primitives/9.0.7": {"runtime": {"lib/net9.0/Microsoft.Extensions.Primitives.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.725.31616"}}}, "Microsoft.IdentityModel.Abstractions/8.13.0": {"runtime": {"lib/net9.0/Microsoft.IdentityModel.Abstractions.dll": {"assemblyVersion": "8.13.0.0", "fileVersion": "8.13.0.60721"}}}, "Microsoft.IdentityModel.JsonWebTokens/8.13.0": {"dependencies": {"Microsoft.IdentityModel.Tokens": "8.13.0"}, "runtime": {"lib/net9.0/Microsoft.IdentityModel.JsonWebTokens.dll": {"assemblyVersion": "8.13.0.0", "fileVersion": "8.13.0.60721"}}}, "Microsoft.IdentityModel.Logging/8.13.0": {"dependencies": {"Microsoft.IdentityModel.Abstractions": "8.13.0"}, "runtime": {"lib/net9.0/Microsoft.IdentityModel.Logging.dll": {"assemblyVersion": "8.13.0.0", "fileVersion": "8.13.0.60721"}}}, "Microsoft.IdentityModel.Protocols/8.0.1": {"dependencies": {"Microsoft.IdentityModel.Tokens": "8.13.0"}, "runtime": {"lib/net9.0/Microsoft.IdentityModel.Protocols.dll": {"assemblyVersion": "8.0.1.0", "fileVersion": "8.0.1.50722"}}}, "Microsoft.IdentityModel.Protocols.OpenIdConnect/8.0.1": {"dependencies": {"Microsoft.IdentityModel.Protocols": "8.0.1", "System.IdentityModel.Tokens.Jwt": "8.13.0"}, "runtime": {"lib/net9.0/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll": {"assemblyVersion": "8.0.1.0", "fileVersion": "8.0.1.50722"}}}, "Microsoft.IdentityModel.Tokens/8.13.0": {"dependencies": {"Microsoft.Extensions.Logging.Abstractions": "9.0.7", "Microsoft.IdentityModel.Logging": "8.13.0"}, "runtime": {"lib/net9.0/Microsoft.IdentityModel.Tokens.dll": {"assemblyVersion": "8.13.0.0", "fileVersion": "8.13.0.60721"}}}, "Microsoft.NETCore.Platforms/1.1.0": {}, "Microsoft.NETCore.Targets/1.1.0": {}, "Microsoft.OpenApi/1.6.23": {"runtime": {"lib/netstandard2.0/Microsoft.OpenApi.dll": {"assemblyVersion": "1.6.23.0", "fileVersion": "1.6.23.0"}}}, "MySqlConnector/2.3.5": {"dependencies": {"Microsoft.Extensions.Logging.Abstractions": "9.0.7"}, "runtime": {"lib/net8.0/MySqlConnector.dll": {"assemblyVersion": "2.0.0.0", "fileVersion": "2.3.5.0"}}}, "Pomelo.EntityFrameworkCore.MySql/8.0.2": {"dependencies": {"Microsoft.EntityFrameworkCore.Relational": "8.0.2", "MySqlConnector": "2.3.5"}, "runtime": {"lib/net8.0/Pomelo.EntityFrameworkCore.MySql.dll": {"assemblyVersion": "8.0.2.0", "fileVersion": "8.0.2.0"}}}, "Swashbuckle.AspNetCore.Swagger/9.0.3": {"dependencies": {"Microsoft.OpenApi": "1.6.23"}, "runtime": {"lib/net9.0/Swashbuckle.AspNetCore.Swagger.dll": {"assemblyVersion": "9.0.3.0", "fileVersion": "9.0.3.1613"}}}, "Swashbuckle.AspNetCore.SwaggerGen/9.0.3": {"dependencies": {"Swashbuckle.AspNetCore.Swagger": "9.0.3"}, "runtime": {"lib/net9.0/Swashbuckle.AspNetCore.SwaggerGen.dll": {"assemblyVersion": "9.0.3.0", "fileVersion": "9.0.3.1613"}}}, "System.IdentityModel.Tokens.Jwt/8.13.0": {"dependencies": {"Microsoft.IdentityModel.JsonWebTokens": "8.13.0", "Microsoft.IdentityModel.Tokens": "8.13.0"}, "runtime": {"lib/net9.0/System.IdentityModel.Tokens.Jwt.dll": {"assemblyVersion": "8.13.0.0", "fileVersion": "8.13.0.60721"}}}, "System.IO/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.Reflection/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.IO": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Reflection.Primitives/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Runtime/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0"}}, "System.Text.Encoding/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Threading.Tasks/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "Application/1.0.0": {"dependencies": {"Domain": "1.0.0", "MediatR": "13.0.0", "Microsoft.Extensions.Caching.Memory": "9.0.7", "Microsoft.Extensions.Configuration": "9.0.7", "Microsoft.Extensions.DependencyInjection": "9.0.7", "System.IdentityModel.Tokens.Jwt": "8.13.0"}, "runtime": {"Application.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "1.0.0.0"}}}, "Domain/1.0.0": {"dependencies": {"MediatR": "13.0.0", "Microsoft.Extensions.Caching.Memory": "9.0.7"}, "runtime": {"Domain.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "1.0.0.0"}}}, "Infrastructure/1.0.0": {"dependencies": {"Domain": "1.0.0", "Microsoft.EntityFrameworkCore": "8.0.2", "Microsoft.Extensions.Configuration": "9.0.7", "Microsoft.Extensions.DependencyInjection": "9.0.7", "Pomelo.EntityFrameworkCore.MySql": "8.0.2", "System.Reflection": "4.3.0"}, "runtime": {"Infrastructure.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "1.0.0.0"}}}}}, "libraries": {"Common/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "MediatR/13.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-gR5vSoIINsXs86we38qdIfG62f6ngxT2sSaePsakJGHJIgLUDA7b41lujGXGWxX0hWyk8suajr2VKcAYSVMtdw==", "path": "mediatr/13.0.0", "hashPath": "mediatr.13.0.0.nupkg.sha512"}, "MediatR.Contracts/2.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-FYv95bNT4UwcNA+G/J1oX5OpRiSUxteXaUt2BJbRSdRNiIUNbggJF69wy6mnk2wYToaanpdXZdCwVylt96MpwQ==", "path": "mediatr.contracts/2.0.1", "hashPath": "mediatr.contracts.2.0.1.nupkg.sha512"}, "Microsoft.AspNetCore.Authentication.JwtBearer/9.0.7": {"type": "package", "serviceable": true, "sha512": "sha512-lloN3XvIgXmdocfghzfszOHJb45OYR3fgOg/h536o+zNB2SmP3JrqeOieCBZ7sipgGcgbxP0boA+loVhdsJxWg==", "path": "microsoft.aspnetcore.authentication.jwtbearer/9.0.7", "hashPath": "microsoft.aspnetcore.authentication.jwtbearer.9.0.7.nupkg.sha512"}, "Microsoft.EntityFrameworkCore/8.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-6QlvBx4rdawW3AkkCsGVV+8qRLk34aknV5JD40s1hbVR18vKmT2KDl2DW83nHcPX7f4oebQ3BD1UMNCI/gkE0g==", "path": "microsoft.entityframeworkcore/8.0.2", "hashPath": "microsoft.entityframeworkcore.8.0.2.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Abstractions/8.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-DjDKp++BTKFZmX+xLTow7grQTY+pImKfhGW68Zf8myiL3zyJ3b8RZbnLsWGNCqKQIF6hJIz/zA/zmERobFwV0A==", "path": "microsoft.entityframeworkcore.abstractions/8.0.2", "hashPath": "microsoft.entityframeworkcore.abstractions.8.0.2.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Analyzers/8.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-LI7awhc0fiAKvcUemsqxXUWqzAH9ywTSyM1rpC1un4p5SE1bhr5nRLvyRVbKRzKakmnNNY3to8NPDnoySEkxVw==", "path": "microsoft.entityframeworkcore.analyzers/8.0.2", "hashPath": "microsoft.entityframeworkcore.analyzers.8.0.2.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Relational/8.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-NoGfcq2OPw0z8XAPf74YFwGlTKjedWdsIEJqq4SvKcPjcu+B+/XDDNrDRxTvILfz4Ug8POSF49s1jz1JvUqTAg==", "path": "microsoft.entityframeworkcore.relational/8.0.2", "hashPath": "microsoft.entityframeworkcore.relational.8.0.2.nupkg.sha512"}, "Microsoft.Extensions.Caching.Abstractions/9.0.7": {"type": "package", "serviceable": true, "sha512": "sha512-30necCQehcg9lFkMEIE7HczcoYGML8GUH6jlincA18d896fLZM9wl5tpTPJHgzANQE/6KXRLZSWbgevgg5csSw==", "path": "microsoft.extensions.caching.abstractions/9.0.7", "hashPath": "microsoft.extensions.caching.abstractions.9.0.7.nupkg.sha512"}, "Microsoft.Extensions.Caching.Memory/9.0.7": {"type": "package", "serviceable": true, "sha512": "sha512-nDu6c8fwrHQYccLnWnvyElrdkL3rZ97TZNqL+niMFUcApVBHdpDmKcRvciGymJ4Y0iLDTOo5J2XhDQEbNb+dFg==", "path": "microsoft.extensions.caching.memory/9.0.7", "hashPath": "microsoft.extensions.caching.memory.9.0.7.nupkg.sha512"}, "Microsoft.Extensions.Configuration/9.0.7": {"type": "package", "serviceable": true, "sha512": "sha512-oxGR51+w5cXm5B9gU6XwpAB2sTiyPSmZm7hjvv0rzRnmL5o/KZzE103AuQj7sK26OBupjVzU/bZxDWvvU4nhEg==", "path": "microsoft.extensions.configuration/9.0.7", "hashPath": "microsoft.extensions.configuration.9.0.7.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Abstractions/9.0.7": {"type": "package", "serviceable": true, "sha512": "sha512-lut/kiVvNsQ120VERMUYSFhpXPpKjjql+giy03LesASPBBcC0o6+aoFdzJH9GaYpFTQ3fGVhVjKjvJDoAW5/IQ==", "path": "microsoft.extensions.configuration.abstractions/9.0.7", "hashPath": "microsoft.extensions.configuration.abstractions.9.0.7.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection/9.0.7": {"type": "package", "serviceable": true, "sha512": "sha512-i05AYA91vgq0as84ROVCyltD2gnxaba/f1Qw2rG7mUsS0gv8cPTr1Gm7jPQHq7JTr4MJoQUcanLVs16tIOUJaQ==", "path": "microsoft.extensions.dependencyinjection/9.0.7", "hashPath": "microsoft.extensions.dependencyinjection.9.0.7.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection.Abstractions/9.0.7": {"type": "package", "serviceable": true, "sha512": "sha512-iPK1FxbGFr2Xb+4Y+dTYI8Gupu9pOi8I3JPuPsrogUmEhe2hzZ9LpCmolMEBhVDo2ikcSr7G5zYiwaapHSQTew==", "path": "microsoft.extensions.dependencyinjection.abstractions/9.0.7", "hashPath": "microsoft.extensions.dependencyinjection.abstractions.9.0.7.nupkg.sha512"}, "Microsoft.Extensions.Logging/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-tvRkov9tAJ3xP51LCv3FJ2zINmv1P8Hi8lhhtcKGqM+ImiTCC84uOPEI4z8Cdq2C3o9e+Aa0Gw0rmrsJD77W+w==", "path": "microsoft.extensions.logging/8.0.0", "hashPath": "microsoft.extensions.logging.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.Abstractions/9.0.7": {"type": "package", "serviceable": true, "sha512": "sha512-sMM6NEAdUTE/elJ2wqjOi0iBWqZmSyaTByLF9e8XHv6DRJFFnOe0N+s8Uc6C91E4SboQCfLswaBIZ+9ZXA98AA==", "path": "microsoft.extensions.logging.abstractions/9.0.7", "hashPath": "microsoft.extensions.logging.abstractions.9.0.7.nupkg.sha512"}, "Microsoft.Extensions.Options/9.0.7": {"type": "package", "serviceable": true, "sha512": "sha512-trJnF6cRWgR5uMmHpGoHmM1wOVFdIYlELlkO9zX+RfieK0321Y55zrcs4AaEymKup7dxgEN/uJU25CAcMNQRXw==", "path": "microsoft.extensions.options/9.0.7", "hashPath": "microsoft.extensions.options.9.0.7.nupkg.sha512"}, "Microsoft.Extensions.Primitives/9.0.7": {"type": "package", "serviceable": true, "sha512": "sha512-ti/zD9BuuO50IqlvhWQs9GHxkCmoph5BHjGiWKdg2t6Or8XoyAfRJiKag+uvd/fpASnNklfsB01WpZ4fhAe0VQ==", "path": "microsoft.extensions.primitives/9.0.7", "hashPath": "microsoft.extensions.primitives.9.0.7.nupkg.sha512"}, "Microsoft.IdentityModel.Abstractions/8.13.0": {"type": "package", "serviceable": true, "sha512": "sha512-gHTIONGSrGAMu6QdUoXsqC7d+sZ3WUHuaMOxpI0SjeMrQsR1kkd5KakpG6UMl1QNFyzctWUCloC5wsG8keDkyQ==", "path": "microsoft.identitymodel.abstractions/8.13.0", "hashPath": "microsoft.identitymodel.abstractions.8.13.0.nupkg.sha512"}, "Microsoft.IdentityModel.JsonWebTokens/8.13.0": {"type": "package", "serviceable": true, "sha512": "sha512-aWSosjvr4cZPqknW+8+6fs0HzD/lAIDFFtI5ajcrxI5CcCg2+YacpPi03oA+prl4sSuh16+HuI/oYq9ZgdlPqA==", "path": "microsoft.identitymodel.jsonwebtokens/8.13.0", "hashPath": "microsoft.identitymodel.jsonwebtokens.8.13.0.nupkg.sha512"}, "Microsoft.IdentityModel.Logging/8.13.0": {"type": "package", "serviceable": true, "sha512": "sha512-ydRP1oDvel5JbuVL2UPWHbXEhECaBmbl7/SPx6J0UeEEiassy62glDyWXNKyKS/gb1MrWp5/Y2TQKEtBDXwPuQ==", "path": "microsoft.identitymodel.logging/8.13.0", "hashPath": "microsoft.identitymodel.logging.8.13.0.nupkg.sha512"}, "Microsoft.IdentityModel.Protocols/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-uA2vpKqU3I2mBBEaeJAWPTjT9v1TZrGWKdgK6G5qJd03CLx83kdiqO9cmiK8/n1erkHzFBwU/RphP83aAe3i3g==", "path": "microsoft.identitymodel.protocols/8.0.1", "hashPath": "microsoft.identitymodel.protocols.8.0.1.nupkg.sha512"}, "Microsoft.IdentityModel.Protocols.OpenIdConnect/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-AQDbfpL+yzuuGhO/mQhKNsp44pm5Jv8/BI4KiFXR7beVGZoSH35zMV3PrmcfvSTsyI6qrcR898NzUauD6SRigg==", "path": "microsoft.identitymodel.protocols.openidconnect/8.0.1", "hashPath": "microsoft.identitymodel.protocols.openidconnect.8.0.1.nupkg.sha512"}, "Microsoft.IdentityModel.Tokens/8.13.0": {"type": "package", "serviceable": true, "sha512": "sha512-25zE+hhKJTOUPh5lxAeHgHiNPDYsPo3iJHd2JVKTalFqMJChJvOkrB5+9PsgKiNm7TpB9h2l6h4AbAl0D3H7OA==", "path": "microsoft.identitymodel.tokens/8.13.0", "hashPath": "microsoft.identitymodel.tokens.8.13.0.nupkg.sha512"}, "Microsoft.NETCore.Platforms/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-kz0PEW2lhqygehI/d6XsPCQzD7ff7gUJaVGPVETX611eadGsA3A877GdSlU0LRVMCTH/+P3o2iDTak+S08V2+A==", "path": "microsoft.netcore.platforms/1.1.0", "hashPath": "microsoft.netcore.platforms.1.1.0.nupkg.sha512"}, "Microsoft.NETCore.Targets/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-aOZA3BWfz9RXjpzt0sRJJMjAscAUm3Hoa4UWAfceV9UTYxgwZ1lZt5nO2myFf+/jetYQo4uTP7zS8sJY67BBxg==", "path": "microsoft.netcore.targets/1.1.0", "hashPath": "microsoft.netcore.targets.1.1.0.nupkg.sha512"}, "Microsoft.OpenApi/1.6.23": {"type": "package", "serviceable": true, "sha512": "sha512-tZ1I0KXnn98CWuV8cpI247A17jaY+ILS9vvF7yhI0uPPEqF4P1d7BWL5Uwtel10w9NucllHB3nTkfYTAcHAh8g==", "path": "microsoft.openapi/1.6.23", "hashPath": "microsoft.openapi.1.6.23.nupkg.sha512"}, "MySqlConnector/2.3.5": {"type": "package", "serviceable": true, "sha512": "sha512-AmEfUPkFl+Ev6jJ8Dhns3CYHBfD12RHzGYWuLt6DfG6/af6YvOMyPz74ZPPjBYQGRJkumD2Z48Kqm8s5DJuhLA==", "path": "mysqlconnector/2.3.5", "hashPath": "mysqlconnector.2.3.5.nupkg.sha512"}, "Pomelo.EntityFrameworkCore.MySql/8.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-XjnlcxVBLnEMbyEc5cZzgZeDyLvAniACZQ04W1slWN0f4rmfNzl98gEMvHnFH0fMDF06z9MmgGi/Sr7hJ+BVnw==", "path": "pomelo.entityframeworkcore.mysql/8.0.2", "hashPath": "pomelo.entityframeworkcore.mysql.8.0.2.nupkg.sha512"}, "Swashbuckle.AspNetCore.Swagger/9.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-CGpkZDWj1g/yH/0wYkxUtBhiFo5TY/Esq2fS0vlBvLOs1UL2Jzef9tdtYmTdd3zBPtnMyXQcsXjMt9yCxz4VaA==", "path": "swashbuckle.aspnetcore.swagger/9.0.3", "hashPath": "swashbuckle.aspnetcore.swagger.9.0.3.nupkg.sha512"}, "Swashbuckle.AspNetCore.SwaggerGen/9.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-STqjhw1TZiEGmIRgE6jcJUOcgU/Fjquc6dP4GqbuwBzqWZAWr/9T7FZOGWYEwKnmkMplzlUNepGHwnUrfTP0fw==", "path": "swashbuckle.aspnetcore.swaggergen/9.0.3", "hashPath": "swashbuckle.aspnetcore.swaggergen.9.0.3.nupkg.sha512"}, "System.IdentityModel.Tokens.Jwt/8.13.0": {"type": "package", "serviceable": true, "sha512": "sha512-GA3moxioWoF/lzRRGNjz+7LD91tajHvS4S6zn3Y3G1p/Koes7pj6gZIt4rzGhe4iIn4rvdj9wxpmN6quObgfMw==", "path": "system.identitymodel.tokens.jwt/8.13.0", "hashPath": "system.identitymodel.tokens.jwt.8.13.0.nupkg.sha512"}, "System.IO/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-3qjaHvxQPDpSOYICjUoTsmoq5u6QJAFRUITgeT/4gqkF1bajbSmb1kwSxEA8AHlofqgcKJcM8udgieRNhaJ5Cg==", "path": "system.io/4.3.0", "hashPath": "system.io.4.3.0.nupkg.sha512"}, "System.Reflection/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-KMiAFoW7MfJGa9nDFNcfu+FpEdiHpWgTcS2HdMpDvt9saK3y/G4GwprPyzqjFH9NTaGPQeWNHU+iDlDILj96aQ==", "path": "system.reflection/4.3.0", "hashPath": "system.reflection.4.3.0.nupkg.sha512"}, "System.Reflection.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-5RXItQz5As4xN2/YUDxdpsEkMhvw3e6aNveFXUn4Hl/udNTCNhnKp8lT9fnc3MhvGKh1baak5CovpuQUXHAlIA==", "path": "system.reflection.primitives/4.3.0", "hashPath": "system.reflection.primitives.4.3.0.nupkg.sha512"}, "System.Runtime/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-JufQi0vPQ0xGnAczR13AUFglDyVYt4Kqnz1AZaiKZ5+GICq0/1MH/mO/eAJHt/mHW1zjKBJd7kV26SrxddAhiw==", "path": "system.runtime/4.3.0", "hashPath": "system.runtime.4.3.0.nupkg.sha512"}, "System.Text.Encoding/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-BiIg+KWaSDOITze6jGQynxg64naAPtqGHBwDrLaCtixsa5bKiR8dpPOHA7ge3C0JJQizJE+sfkz1wV+BAKAYZw==", "path": "system.text.encoding/4.3.0", "hashPath": "system.text.encoding.4.3.0.nupkg.sha512"}, "System.Threading.Tasks/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-LbSxKEdOUhVe8BezB/9uOGGppt+nZf6e1VFyw6v3DN6lqitm0OSn2uXMOdtP0M3W4iMcqcivm2J6UgqiwwnXiA==", "path": "system.threading.tasks/4.3.0", "hashPath": "system.threading.tasks.4.3.0.nupkg.sha512"}, "Application/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Domain/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Infrastructure/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}}}